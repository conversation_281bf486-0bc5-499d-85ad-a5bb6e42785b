#!/usr/bin/env python3
import re
import sys

def fix_log_calls(content):
    # 替换 LOG_INFO 调用
    content = re.sub(r'LOG_INFO\("\[([^\]]+)\] ([^"]*)", ([^)]+)\);', 
                     r'std::cout << "[\1] \2" << \3 << std::endl;', content)
    
    content = re.sub(r'LOG_INFO\("\[([^\]]+)\] ([^"]*)", ([^,]+), ([^)]+)\);', 
                     r'std::cout << "[\1] \2" << \3 << " " << \4 << std::endl;', content)
    
    content = re.sub(r'LOG_INFO\("\[([^\]]+)\] ([^"]*)", ([^,]+), ([^,]+), ([^)]+)\);', 
                     r'std::cout << "[\1] \2" << \3 << " " << \4 << " " << \5 << std::endl;', content)
    
    # 替换简单的 LOG_INFO 调用（无参数）
    content = re.sub(r'LOG_INFO\("\[([^\]]+)\] ([^"]*)"[^)]*\);', 
                     r'std::cout << "[\1] \2" << std::endl;', content)
    
    # 替换 LOG_ERROR 调用
    content = re.sub(r'LOG_ERROR\("\[([^\]]+)\] ([^"]*)", ([^)]+)\);', 
                     r'std::cout << "[\1] ERROR: \2" << \3 << std::endl;', content)
    
    content = re.sub(r'LOG_ERROR\("\[([^\]]+)\] ([^"]*)", ([^,]+), ([^)]+)\);', 
                     r'std::cout << "[\1] ERROR: \2" << \3 << " " << \4 << std::endl;', content)
    
    # 替换简单的 LOG_ERROR 调用（无参数）
    content = re.sub(r'LOG_ERROR\("\[([^\]]+)\] ([^"]*)"[^)]*\);', 
                     r'std::cout << "[\1] ERROR: \2" << std::endl;', content)
    
    # 替换 LOG_WARN 调用
    content = re.sub(r'LOG_WARN\("\[([^\]]+)\] ([^"]*)", ([^)]+)\);', 
                     r'std::cout << "[\1] WARN: \2" << \3 << std::endl;', content)
    
    content = re.sub(r'LOG_WARN\("\[([^\]]+)\] ([^"]*)", ([^,]+), ([^)]+)\);', 
                     r'std::cout << "[\1] WARN: \2" << \3 << " " << \4 << std::endl;', content)
    
    # 替换简单的 LOG_WARN 调用（无参数）
    content = re.sub(r'LOG_WARN\("\[([^\]]+)\] ([^"]*)"[^)]*\);', 
                     r'std::cout << "[\1] WARN: \2" << std::endl;', content)
    
    return content

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 fix_logs.py <file>")
        sys.exit(1)
    
    filename = sys.argv[1]
    
    with open(filename, 'r') as f:
        content = f.read()
    
    fixed_content = fix_log_calls(content)
    
    with open(filename, 'w') as f:
        f.write(fixed_content)
    
    print(f"Fixed log calls in {filename}")

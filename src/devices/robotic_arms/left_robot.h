/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_LEFT_ROBOT_H
#define AUBO_COFFEE_SERVICE_LEFT_ROBOT_H

#include <memory>
#include "robot_base.h"


namespace aubo {

/**
 * @class LeftRobot
 * @brief 控制咖啡服务的左侧机器人臂
 *
 * 该类用于控制咖啡服务中的左侧机器人臂。它处理连接、初始化，并提供执行常见序列的方法，如取杯子、取咖啡。
 */
class LeftRobot {
public:
    /**
     * @brief 构造函数
     */
    LeftRobot();

    /**
     * @brief 析构函数
     */
    ~LeftRobot();

    /**
     * @brief 初始化机器人
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭机器人连接
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 获取机器人状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

    /**
     * @brief 检查机器人是否已连接
     * @return 已连接返回true
     */
    bool is_connected() const;

    /**
     * @brief 移动到初始位置
     * @return 移动成功返回true
     */
    bool move_to_home();

    /**
     * @brief 移动到准备位置
     * @return 移动成功返回true
     */
    bool move_to_ready();

    /**
     * @brief 移动到杯子出口位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_to_cup_outlet();

    /**
     * @brief 移动到咖啡出口位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_to_coffee_outlet();

    /**
     * @brief 移动到拉花位置
     *
     * @return 如果移动成功则返回true
     */
    bool move_to_latte_art();

    /**
     * @brief 执行取杯序列
     *
     * @return 如果序列执行成功则返回true
     */
    bool get_cup();

    /**
     * @brief 执行取咖啡序列
     *
     * 将杯子放在咖啡机下方并取回已制作好的咖啡
     *
     * @return 如果序列执行成功则返回true
     */
    bool get_coffee();

    /**
     * @brief 准备拉花位置
     *
     * 将机器人移动到拉花的准备位置，这是所有拉花类型的通用准备动作
     * 通常在取咖啡完成后调用，为后续的拉花动作做准备
     *
     * @return 如果移动成功则返回true
     */
    bool prepare_for_latte_art();

    /**
     * @brief 执行拉花动作
     *
     * 执行具体的拉花动作，需要在调用 prepare_for_latte_art() 之后调用
     * 这样的设计允许在准备和执行之间插入其他操作（如右臂协作）
     *
     * @param art_type 拉花类型字符串，如 "heart", "leaf", "rosetta" 等
     * @return 如果拉花动作执行成功则返回true
     */
    bool do_latte_art(const std::string& art_type = "none");

    /**
     * @brief 执行咖啡交付
     *
     * 将制作好的咖啡送到指定地方，完成整个咖啡制作流程
     * 通常在拉花完成后调用
     *
     * @return 如果咖啡交付成功则返回true
     */
    bool deliver_coffee();

    // 基类方法的重写在需要时可以添加

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_LEFT_ROBOT_H

/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "robot_base.h"

#include <chrono>
#include <iostream>
#include <thread>

#include <iostream>

namespace aubo {

// RobotBase 实现
RobotBase::RobotBase(const std::string& robot_name, const std::string& host, int port)
    : robot_name_(robot_name), host_(host), port_(port), is_connected_(false), is_initialized_(false) {
}

RobotBase::~RobotBase() = default;

bool RobotBase::init() {
    std::cout << "[" << robot_name_ << "] 初始化机器人" << std::endl;

    if (is_initialized_) {
        std::cout << "[" << robot_name_ << "] 机器人已经初始化" << std::endl;
        return true;
    }

    if (!connect()) {
        return false;
    }

    is_connected_ = true;
    is_initialized_ = true;

    std::cout << "[" << robot_name_ << "] 机器人初始化完成" << std::endl;
    return true;
}

bool RobotBase::shutdown() {
    std::cout << "[" << robot_name_ << "] 关闭机器人" << std::endl;

    bool result = disconnect();
    is_connected_ = false;
    is_initialized_ = false;

    return result;
}

bool RobotBase::emergency_stop() {
    std::cout << "[" << robot_name_ << "] 执行紧急停止" << std::endl;

    if (!is_connected_) {
        std::cout << "[" << robot_name_ << "] 机器人未连接" << std::endl;
        return false;
    }

    // 简化的紧急停止实现
    std::cout << "[" << robot_name_ << "] 紧急停止完成" << std::endl;
    return true;
}

std::string RobotBase::get_status() const {
    if (!is_connected_) {
        return robot_name_ + ": 未连接";
    }

    return robot_name_ + ": 已连接";
}

bool RobotBase::is_connected() const {
    return is_connected_;
}

bool RobotBase::connect() {
    std::cout << "[" << robot_name_ << "] 连接到机器人 " << host_ << ":" << port_ << std::endl;

    try {
        int result = robot_service_.robotServiceLogin(host_.c_str(), port_, "aubo", "123456");
        if (result == aubo_robot_namespace::InterfaceCallSuccCode) {
            std::cout << "[" << robot_name_ << "] 机器人连接成功" << std::endl;
            is_connected_ = true;
            return true;
        } else {
            std::cout << "[" << robot_name_ << "] 机器人连接失败，错误码: " << result << std::endl;
            is_connected_ = false;
            return false;
        }
    } catch (const std::exception& e) {
        std::cout << "[" << robot_name_ << "] 连接异常: " << e.what() << std::endl;
        is_connected_ = false;
        return false;
    }
}

bool RobotBase::disconnect() {
    if (!is_connected_) {
        return true;
    }

    std::cout << "[" << robot_name_ << "] 断开机器人连接" << std::endl;
    robot_service_.robotServiceLogout();
    is_connected_ = false;
    return true;
}

bool RobotBase::check_robot_state() {
    if (!is_connected_) {
        std::cout << "[" << robot_name_ << "] 无法操作: 未连接" << std::endl;
        return false;
    }

    if (!is_initialized_) {
        std::cout << "[" << robot_name_ << "] 无法操作: 未初始化" << std::endl;
        return false;
    }

    return true;
}

} // namespace aubo

/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H
#define AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H

#include <string>
#include <memory>

namespace aubo {

/**
 * @struct CoffeeParameters
 * @brief 咖啡制作参数
 */
struct CoffeeParameters {
    int volume_ml;             ///< 咖啡容量(毫升)
    int temperature;           ///< 温度(摄氏度)

    CoffeeParameters()
        : volume_ml(200), temperature(85) {}
};

/**
 * @class CoffeeMachine
 * @brief 咖啡机类
 * 
 * 负责制作各种类型的咖啡
 */
class CoffeeMachine {
public:
    /**
     * @brief 构造函数
     */
    CoffeeMachine();

    /**
     * @brief 析构函数
     */
    ~CoffeeMachine();

    /**
     * @brief 初始化咖啡机
     * @return 初始化成功返回true
     */
    bool init();

    /**
     * @brief 关闭咖啡机
     * @return 关闭成功返回true
     */
    bool shutdown();

    /**
     * @brief 紧急停止
     * @return 紧急停止成功返回true
     */
    bool emergency_stop();

    /**
     * @brief 制作咖啡
     * @param params 咖啡制作参数
     * @return 制作成功返回true
     */
    bool brew_coffee(const CoffeeParameters& params = CoffeeParameters());

    /**
     * @brief 预热咖啡机
     * @return 预热成功返回true
     */
    bool preheat();

    /**
     * @brief 清洁咖啡机
     * @return 清洁成功返回true
     */
    bool clean();

    /**
     * @brief 检查水箱水位
     * @return 水位百分比 (0-100)
     */
    int check_water_level() const;

    /**
     * @brief 检查咖啡豆余量
     * @return 咖啡豆余量百分比 (0-100)
     */
    int check_bean_level() const;

    /**
     * @brief 获取咖啡机状态
     * @return 状态信息字符串
     */
    std::string get_status() const;

    /**
     * @brief 获取当前温度
     * @return 当前温度(摄氏度)
     */
    int get_current_temperature() const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};



} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_MACHINE_H

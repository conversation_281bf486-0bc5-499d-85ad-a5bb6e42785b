/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "milk_container_cleaner.h"

#include <atomic>
#include <chrono>
#include <sstream>
#include <thread>

#include <aubo-base/log.h>

namespace aubo {

class MilkContainerCleaner::Impl {
public:
    Impl() : detergent_level_(80), cleaning_status_(CleaningStatus::IDLE), 
             cleaning_progress_(0), is_paused_(false) {
    }

    bool init() {
        LOG_INFO("[MilkContainerCleaner] 初始化牛奶容器清洁器");

        // 模拟初始化过程
        std::this_thread::sleep_for(std::chrono::milliseconds(800));

        LOG_INFO("[MilkContainerCleaner] 牛奶容器清洁器初始化完成");
        return true;
    }

    bool shutdown() {
        LOG_INFO("[MilkContainerCleaner] 关闭牛奶容器清洁器");

        // 如果正在清洁，先停止
        if (cleaning_status_ != CleaningStatus::IDLE) {
            stop_cleaning();
        }

        return true;
    }

    bool emergency_stop() {
        LOG_WARN("[MilkContainerCleaner] 牛奶容器清洁器紧急停止");
        cleaning_status_ = CleaningStatus::IDLE;
        cleaning_progress_ = 0;
        is_paused_ = false;
        return true;
    }

    bool start_cleaning(const CleaningParameters& params) {
        LOG_INFO("[MilkContainerCleaner] 开始清洁 - 模式: {}, 温度: {}°C, 时长: {}秒",
                 cleaning_mode_to_string(params.mode),
                 params.temperature,
                 params.duration);

        if (cleaning_status_ != CleaningStatus::IDLE) {
            LOG_ERROR("[MilkContainerCleaner] 清洁器正在运行，无法开始新的清洁");
            return false;
        }

        if (params.use_detergent && detergent_level_ < 10) {
            LOG_ERROR("[MilkContainerCleaner] 清洁剂不足，无法开始清洁");
            return false;
        }

        // 开始清洁过程
        cleaning_status_ = CleaningStatus::CLEANING;
        cleaning_progress_ = 0;
        is_paused_ = false;

        // 启动清洁线程
        cleaning_thread_ = std::thread(&Impl::cleaning_process, this, params);
        cleaning_thread_.detach();

        return true;
    }

    bool stop_cleaning() {
        LOG_INFO("[MilkContainerCleaner] 停止清洁");

        if (cleaning_status_ == CleaningStatus::IDLE) {
            LOG_WARN("[MilkContainerCleaner] 清洁器未在运行");
            return true;
        }

        cleaning_status_ = CleaningStatus::IDLE;
        cleaning_progress_ = 0;
        is_paused_ = false;

        return true;
    }

    bool pause_cleaning() {
        LOG_INFO("[MilkContainerCleaner] 暂停清洁");

        if (cleaning_status_ == CleaningStatus::IDLE) {
            LOG_ERROR("[MilkContainerCleaner] 清洁器未在运行，无法暂停");
            return false;
        }

        is_paused_ = true;
        return true;
    }

    bool resume_cleaning() {
        LOG_INFO("[MilkContainerCleaner] 恢复清洁");

        if (cleaning_status_ == CleaningStatus::IDLE) {
            LOG_ERROR("[MilkContainerCleaner] 清洁器未在运行，无法恢复");
            return false;
        }

        is_paused_ = false;
        return true;
    }

    CleaningStatus get_cleaning_status() const {
        return cleaning_status_;
    }

    int get_cleaning_progress() const {
        return cleaning_progress_;
    }

    int check_detergent_level() const {
        return detergent_level_;
    }

    bool refill_detergent() {
        LOG_INFO("[MilkContainerCleaner] 补充清洁剂");
        detergent_level_ = 100;
        LOG_INFO("[MilkContainerCleaner] 清洁剂补充完成，当前余量: {}%", detergent_level_);
        return true;
    }

    std::string get_status() const {
        std::ostringstream oss;
        oss << "牛奶容器清洁器状态:\n";
        oss << "  清洁状态: " << cleaning_status_to_string(cleaning_status_) << "\n";
        oss << "  清洁进度: " << cleaning_progress_ << "%\n";
        oss << "  清洁剂余量: " << detergent_level_ << "%\n";
        oss << "  暂停状态: " << (is_paused_ ? "已暂停" : "运行中") << "\n";
        return oss.str();
    }

private:
    void cleaning_process(const CleaningParameters& params) {
        try {
            // 清洁阶段
            cleaning_status_ = CleaningStatus::CLEANING;
            simulate_phase("清洁", params.duration * 0.6, 0, 60);
            
            if (cleaning_status_ == CleaningStatus::IDLE) return; // 被停止
            
            // 冲洗阶段
            cleaning_status_ = CleaningStatus::RINSING;
            simulate_phase("冲洗", params.duration * 0.3, 60, 90);
            
            if (cleaning_status_ == CleaningStatus::IDLE) return; // 被停止
            
            // 烘干阶段
            cleaning_status_ = CleaningStatus::DRYING;
            simulate_phase("烘干", params.duration * 0.1, 90, 100);
            
            if (cleaning_status_ == CleaningStatus::IDLE) return; // 被停止
            
            // 完成
            cleaning_status_ = CleaningStatus::COMPLETED;
            cleaning_progress_ = 100;
            
            // 消耗清洁剂
            if (params.use_detergent) {
                int consumption = get_detergent_consumption(params.mode);
                detergent_level_ = std::max(0, detergent_level_ - consumption);
            }
            
            LOG_INFO("[MilkContainerCleaner] 清洁完成");

            // 2秒后回到空闲状态
            std::this_thread::sleep_for(std::chrono::seconds(2));
            cleaning_status_ = CleaningStatus::IDLE;
            cleaning_progress_ = 0;

        } catch (const std::exception& e) {
            LOG_ERROR("[MilkContainerCleaner] 清洁过程异常: {}", e.what());
            cleaning_status_ = CleaningStatus::ERROR;
        }
    }

    void simulate_phase(const std::string& phase_name, double duration_seconds, int start_progress, int end_progress) {
        LOG_INFO("[MilkContainerCleaner] 开始{}阶段", phase_name);

        int steps = 10;
        double step_duration = duration_seconds / steps;
        int progress_step = (end_progress - start_progress) / steps;

        for (int i = 0; i < steps; ++i) {
            if (cleaning_status_ == CleaningStatus::IDLE) return; // 被停止

            // 处理暂停
            while (is_paused_ && cleaning_status_ != CleaningStatus::IDLE) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            cleaning_progress_ = start_progress + (i + 1) * progress_step;
            std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(step_duration * 1000)));
        }

        LOG_INFO("[MilkContainerCleaner] {}阶段完成", phase_name);
    }

    int get_detergent_consumption(CleaningMode mode) const {
        switch (mode) {
            case CleaningMode::QUICK: return 5;
            case CleaningMode::STANDARD: return 10;
            case CleaningMode::DEEP: return 15;
            default: return 10;
        }
    }

private:
    int detergent_level_;                   ///< 清洁剂余量百分比
    std::atomic<CleaningStatus> cleaning_status_;  ///< 清洁状态
    std::atomic<int> cleaning_progress_;    ///< 清洁进度
    std::atomic<bool> is_paused_;           ///< 是否暂停
    std::thread cleaning_thread_;           ///< 清洁线程
};

// MilkContainerCleaner 公共接口实现
MilkContainerCleaner::MilkContainerCleaner() {
    impl_ = std::make_unique<Impl>();
}

MilkContainerCleaner::~MilkContainerCleaner() = default;

bool MilkContainerCleaner::init() {
    return impl_->init();
}

bool MilkContainerCleaner::shutdown() {
    return impl_->shutdown();
}

bool MilkContainerCleaner::emergency_stop() {
    return impl_->emergency_stop();
}

bool MilkContainerCleaner::start_cleaning(const CleaningParameters& params) {
    return impl_->start_cleaning(params);
}

bool MilkContainerCleaner::stop_cleaning() {
    return impl_->stop_cleaning();
}

bool MilkContainerCleaner::pause_cleaning() {
    return impl_->pause_cleaning();
}

bool MilkContainerCleaner::resume_cleaning() {
    return impl_->resume_cleaning();
}

CleaningStatus MilkContainerCleaner::get_cleaning_status() const {
    return impl_->get_cleaning_status();
}

int MilkContainerCleaner::get_cleaning_progress() const {
    return impl_->get_cleaning_progress();
}

int MilkContainerCleaner::check_detergent_level() const {
    return impl_->check_detergent_level();
}

bool MilkContainerCleaner::refill_detergent() {
    return impl_->refill_detergent();
}

std::string MilkContainerCleaner::get_status() const {
    return impl_->get_status();
}

// 辅助函数实现
std::string cleaning_mode_to_string(CleaningMode mode) {
    switch (mode) {
        case CleaningMode::QUICK: return "快速清洁";
        case CleaningMode::STANDARD: return "标准清洁";
        case CleaningMode::DEEP: return "深度清洁";
        default: return "未知模式";
    }
}

CleaningMode string_to_cleaning_mode(const std::string& mode_string) {
    if (mode_string == "quick") return CleaningMode::QUICK;
    if (mode_string == "deep") return CleaningMode::DEEP;
    return CleaningMode::STANDARD;
}

std::string cleaning_status_to_string(CleaningStatus status) {
    switch (status) {
        case CleaningStatus::IDLE: return "空闲";
        case CleaningStatus::CLEANING: return "清洁中";
        case CleaningStatus::RINSING: return "冲洗中";
        case CleaningStatus::DRYING: return "烘干中";
        case CleaningStatus::COMPLETED: return "完成";
        case CleaningStatus::ERROR: return "错误";
        default: return "未知状态";
    }
}

} // namespace aubo
